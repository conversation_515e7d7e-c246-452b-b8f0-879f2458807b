import { ComponentType } from '@angular/cdk/portal';
import { Observable } from 'rxjs';

import {
    DefaultRightSideComponent,
    DefaultRightSideComponentInputs,
} from ':shared/components/stepper-modal/stepper-modal-right-side/stepper-modal-right-side.component';

import { BaseStepComponent } from '../components/stepper-modal/base-step.component';

export interface Step<
    T = unknown,
    U = unknown,
    V extends DefaultRightSideComponent = DefaultRightSideComponent,
    W extends DefaultRightSideComponentInputs<V> = DefaultRightSideComponentInputs<V>,
> {
    component: ComponentType<BaseStepComponent<T, U>>;
    subtitle?: string;
    primaryButtonText?: string;
    secondaryButtonText?: string;
    hideSecondaryButton?: boolean;
    rightSide?: {
        component: ComponentType<V>;
        inputs: W;
    };

    nextFunction$: (inputData: T) => Observable<unknown>;
}
