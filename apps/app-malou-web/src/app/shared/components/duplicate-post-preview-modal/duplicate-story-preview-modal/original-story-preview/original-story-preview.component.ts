import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { PlatformOption } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.component';
import { StoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { DefaultRightSideComponent } from ':shared/components/stepper-modal/stepper-modal-right-side/stepper-modal-right-side.component';

@Component({
    selector: 'app-original-story-preview',
    imports: [StoriesPreviewsComponent],
    templateUrl: './original-story-preview.component.html',
    styleUrl: './original-story-preview.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OriginalStoryPreviewComponent implements DefaultRightSideComponent{
    readonly story = input.required<StoryToDuplicate>();
    readonly selectedPreviewPlatform = input<PlatformOption | null>(null);

    readonly storyForPreview = computed(() => StoryItem.fromStoryToDuplicate(this.story()));
}
