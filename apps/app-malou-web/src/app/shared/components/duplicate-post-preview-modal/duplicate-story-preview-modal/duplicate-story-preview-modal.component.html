<div class="flex h-full w-[80vw] p-7">
    <!-- <div class="flex grow flex-col gap-6 p-6">
        <div class="flex flex-col">
            <div class="malou-text-18--bold text-malou-color-text-1">
                {{ 'stories.duplicate_story_preview_modal.title' | translate }}
            </div>
            <div class="malou-text-12--regular text-malou-color-text-2">
                {{ 'stories.duplicate_story_preview_modal.subtitle' | translate }}
            </div>
        </div> -->

    <div class="flex w-full flex-col gap-4">
        @for (postForm of previewCaptionPostForms.controls; track postForm; let isLast = $last; let isFirst = $first; let index = $index) {
            <app-story-preview-card
                [shouldDisplayDateForm]="!willPostAllAtSameTime()"
                [postForm]="postForm"
                [shouldDisplayColumnHeader]="isFirst"></app-story-preview-card>
            @if (!isLast) {
                <mat-divider class="!-mb-2 !border-malou-color-border-primary"></mat-divider>
            }
        }
        <!-- </div> -->
    </div>

    <!-- <div class="flex h-full flex-col gap-4 border-l border-malou-color-border-primary bg-malou-color-background-light p-6">
        <div class="flex justify-between">
            <span class="malou-text-18--bold text-malou-color-text-1">
                {{ 'stories.duplicate_story_preview_modal.preview.title' | translate }}
            </span>
            <button class="malou-btn-icon" mat-icon-button (click)="onClose()">
                <mat-icon color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
            </button>
        </div>

        <div class="overflow-hidden rounded-[10px]">
            <app-stories-previews
                [selectedPreviewPlatform]="selectedPreviewPlatform"
                [stories]="[storyForPreview]"
                [showStatus]="false"></app-stories-previews>
        </div>
    </div> -->
</div>
