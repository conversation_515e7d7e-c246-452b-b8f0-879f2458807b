import { Component, input } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { StepperModalRightSideComponent } from './stepper-modal-right-side.component';

// Test component to be dynamically loaded
@Component({
    selector: 'app-test-component',
    template: `
        <div data-testid="test-component">
            <h3>{{ title() }}</h3>
            <p>{{ description() }}</p>
        </div>
    `,
    standalone: true,
})
class TestComponent {
    readonly title = input.required<string>();
    readonly description = input<string>('Default description');
}

describe('StepperModalRightSideComponent', () => {
    let component: StepperModalRightSideComponent<typeof TestComponent, any>;
    let fixture: ComponentFixture<StepperModalRightSideComponent<typeof TestComponent, any>>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [StepperModalRightSideComponent],
        }).compileComponents();

        fixture = TestBed.createComponent(StepperModalRightSideComponent);
        component = fixture.componentInstance;
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should load component dynamically when inputs are provided', async () => {
        // Set the component and inputs
        fixture.componentRef.setInput('component', TestComponent);
        fixture.componentRef.setInput('componentInputs', {
            title: 'Test Title',
            description: 'Test Description',
        });

        fixture.detectChanges();
        await fixture.whenStable();

        // Check if the dynamic component was loaded
        const testElement = fixture.nativeElement.querySelector('[data-testid="test-component"]');
        expect(testElement).toBeTruthy();
        expect(testElement.querySelector('h3')?.textContent).toBe('Test Title');
        expect(testElement.querySelector('p')?.textContent).toBe('Test Description');
    });

    it('should update component when inputs change', async () => {
        // Initial setup
        fixture.componentRef.setInput('component', TestComponent);
        fixture.componentRef.setInput('componentInputs', {
            title: 'Initial Title',
            description: 'Initial Description',
        });

        fixture.detectChanges();
        await fixture.whenStable();

        // Update inputs
        fixture.componentRef.setInput('componentInputs', {
            title: 'Updated Title',
            description: 'Updated Description',
        });

        fixture.detectChanges();
        await fixture.whenStable();

        // Check if the component was updated
        const testElement = fixture.nativeElement.querySelector('[data-testid="test-component"]');
        expect(testElement.querySelector('h3')?.textContent).toBe('Updated Title');
        expect(testElement.querySelector('p')?.textContent).toBe('Updated Description');
    });

    it('should clear previous component when loading new one', async () => {
        // Create another test component
        @Component({
            selector: 'app-another-test-component',
            template: '<div data-testid="another-test-component">Another Component</div>',
            standalone: true,
        })
        class AnotherTestComponent {}

        // Load first component
        fixture.componentRef.setInput('component', TestComponent);
        fixture.componentRef.setInput('componentInputs', {
            title: 'Test Title',
        });

        fixture.detectChanges();
        await fixture.whenStable();

        expect(fixture.nativeElement.querySelector('[data-testid="test-component"]')).toBeTruthy();

        // Load second component
        fixture.componentRef.setInput('component', AnotherTestComponent);
        fixture.componentRef.setInput('componentInputs', {});

        fixture.detectChanges();
        await fixture.whenStable();

        // First component should be gone, second should be present
        expect(fixture.nativeElement.querySelector('[data-testid="test-component"]')).toBeFalsy();
        expect(fixture.nativeElement.querySelector('[data-testid="another-test-component"]')).toBeTruthy();
    });
});
