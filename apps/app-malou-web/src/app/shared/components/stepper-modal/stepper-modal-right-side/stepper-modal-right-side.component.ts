import { Component, effect, input, Type, viewChild } from '@angular/core';

import { DynamicComponentDirective } from ':shared/directives/dynamic-component.directive';
import { Props } from ':shared/helpers/props';

/**
 * Base component type for components that can be dynamically loaded in the stepper modal right side.
 * Components extending this type should be standalone Angular components.
 */
export type DefaultRightSideComponent = Type<any>;

/**
 * Type helper to extract input properties from a component type.
 * This ensures type safety when passing inputs to dynamically loaded components.
 */
export type DefaultRightSideComponentInputs<T extends DefaultRightSideComponent> = Props<InstanceType<T>>;

@Component({
    selector: 'app-stepper-modal-right-side',
    templateUrl: './stepper-modal-right-side.component.html',
    styleUrls: ['./stepper-modal-right-side.component.scss'],
    imports: [DynamicComponentDirective],
    standalone: true,
})
export class StepperModalRightSideComponent<T extends BaseRightSideComponent<U>, U extends BaseRightSideComponentInputs<T>> {
    readonly component = input.required<T>();
    readonly componentInputs = input.required<U>();

    readonly dynamicComponent = viewChild(DynamicComponentDirective);

    constructor() {
        effect(() => {
            const dynamicComponent = this.dynamicComponent();
            const component = this.component();
            if (!dynamicComponent || !component) {
                return;
            }
            this._loadComponent(dynamicComponent, component);
        });
    }

    private _loadComponent(dynamicComponent: DynamicComponentDirective, component: T): void {
        const viewContainerRef = dynamicComponent.viewContainerRef;
        viewContainerRef.clear();
        const componentRef = viewContainerRef.createComponent(component);
        const inputs = this.componentInputs();
        if (inputs && typeof inputs === 'object') {
            Object.entries(inputs).forEach(([key, value]) => {
                componentRef.setInput(key, value);
            });
        }
    }
}
