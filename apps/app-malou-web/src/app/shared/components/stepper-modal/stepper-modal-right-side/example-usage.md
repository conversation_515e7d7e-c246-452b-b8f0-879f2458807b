# StepperModalRightSideComponent Usage Example

This document shows how to use the `StepperModalRightSideComponent` to dynamically load components with type safety.

## Basic Usage

### 1. Create a Component to be Dynamically Loaded

```typescript
import { Component, input } from '@angular/core';

@Component({
  selector: 'app-example-right-side',
  template: `
    <div class="p-4">
      <h3>{{ title() }}</h3>
      <p>{{ description() }}</p>
      <div *ngIf="data()">
        <pre>{{ data() | json }}</pre>
      </div>
    </div>
  `,
  standalone: true,
})
export class ExampleRightSideComponent {
  readonly title = input.required<string>();
  readonly description = input<string>('Default description');
  readonly data = input<any>();
}
```

### 2. Use the StepperModalRightSideComponent

```typescript
import { Component } from '@angular/core';
import { StepperModalRightSideComponent } from './stepper-modal-right-side.component';
import { ExampleRightSideComponent } from './example-right-side.component';

@Component({
  selector: 'app-parent',
  template: `
    <app-stepper-modal-right-side
      [component]="rightSideComponent"
      [componentInputs]="rightSideInputs"
    />
  `,
  imports: [StepperModalRightSideComponent],
  standalone: true,
})
export class ParentComponent {
  readonly rightSideComponent = ExampleRightSideComponent;
  readonly rightSideInputs = {
    title: 'Dynamic Component Title',
    description: 'This component was loaded dynamically!',
    data: { userId: 123, status: 'active' }
  };
}
```

## Type Safety

The component provides full type safety:

- `DefaultRightSideComponent`: Type for any Angular component that can be dynamically loaded
- `DefaultRightSideComponentInputs<T>`: Extracts the input properties from a component type

This ensures that:
1. Only valid component types can be passed to the `component` input
2. The `componentInputs` must match the actual inputs of the component
3. TypeScript will provide autocompletion and error checking

## Advanced Usage

### With Complex Input Types

```typescript
interface UserData {
  id: number;
  name: string;
  email: string;
}

@Component({
  selector: 'app-user-details',
  template: `
    <div class="user-card">
      <h4>{{ user().name }}</h4>
      <p>{{ user().email }}</p>
      <button (click)="onEdit()" [disabled]="!editable()">
        Edit User
      </button>
    </div>
  `,
  standalone: true,
})
export class UserDetailsComponent {
  readonly user = input.required<UserData>();
  readonly editable = input<boolean>(false);
  readonly onEdit = input<() => void>(() => {});
}
```

### Usage with Signal Inputs

```typescript
@Component({
  selector: 'app-parent-with-signals',
  template: `
    <app-stepper-modal-right-side
      [component]="userComponent"
      [componentInputs]="userInputs()"
    />
  `,
  imports: [StepperModalRightSideComponent],
  standalone: true,
})
export class ParentWithSignalsComponent {
  readonly userComponent = UserDetailsComponent;
  readonly selectedUser = signal<UserData>({ id: 1, name: 'John', email: '<EMAIL>' });
  readonly isEditable = signal(true);

  readonly userInputs = computed(() => ({
    user: this.selectedUser(),
    editable: this.isEditable(),
    onEdit: () => this.editUser()
  }));

  editUser() {
    console.log('Editing user:', this.selectedUser());
  }
}
```

## Key Features

1. **Type Safety**: Full TypeScript support with input validation
2. **Dynamic Loading**: Components are loaded dynamically at runtime
3. **Signal Support**: Works with Angular's modern signal-based inputs
4. **Automatic Cleanup**: Components are properly cleaned up when changed
5. **Input Binding**: All component inputs are automatically bound using `setInput()`

## Notes

- The dynamically loaded component should be standalone
- All inputs are set using Angular's `setInput()` method
- The component is recreated when the `component` input changes
- ViewContainerRef is cleared before loading a new component
