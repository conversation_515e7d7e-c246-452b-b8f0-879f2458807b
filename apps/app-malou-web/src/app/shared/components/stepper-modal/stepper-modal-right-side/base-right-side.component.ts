import { ChangeDetectionStrategy, Component, input } from '@angular/core';

export type BaseRightSideComponentInputs<T extends BaseRightSideComponent<any>> = T extends BaseRightSideComponent<infer U> ? U : never;

@Component({
    template: '',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseRightSideComponent<T extends Record<string, any>> {
    readonly inputs = input<T | {}>({});
}
